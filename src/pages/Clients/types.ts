
export type UUID = `${string}-${string}-${string}-${string}-${string}`

export type Client = {
  id: UUID
  name: string
  code: string
  email: string
  phone: string
  address: string
  website?: string
  logoUrl?: string
  isActive: boolean
  subscriptionPlan?: string
  subscriptionStartDate?: string
  subscriptionEndDate?: string
  updatedAt: string
  createdAt: string
}

export type EmptyClient = Omit<Client, 'id' | 'updatedAt' | 'createdAt'> & {
  name: string
  code: string
  email: string
  phone: string
  address: string
  website?: string
  logoUrl?: string
  isActive?: boolean
  subscriptionPlan?: string
  subscriptionStartDate?: string
  subscriptionEndDate?: string
}
