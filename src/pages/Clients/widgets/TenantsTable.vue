<script setup lang="ts">
import {computed, PropType} from 'vue'
import {defineVaDataTableColumns} from 'vuestic-ui'
import {Tenant} from '../types'
import {Pagination, Sorting} from '../../../data/pages/tenants'
import {useVModel} from '@vueuse/core'
import {formatDateTime} from "../../../services/utils";
import TenantsStatusBadge from "../components/TenantsStatusBadge.vue";

const columns = defineVaDataTableColumns([
    {label: 'Name', key: 'name', sortable: true},
    {label: 'Code', key: 'code', sortable: true},
    {label: 'Email', key: 'email', sortable: true},
    {label: 'Phone', key: 'phone', sortable: true},
    {label: 'Address', key: 'address', sortable: true},
    {label: 'Status', key: 'isActive', sortable: true},
    {label: 'Update Date', key: 'updatedAt', sortable: true},
    {label: 'Creation Date', key: 'createdAt', sortable: true},
    {label: ' ', key: 'actions'},
])

const props = defineProps({
    tenants: {
        type: Array as PropType<Tenant[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
    sortBy: {
        type: String as PropType<Sorting['sortBy']>,
        default: undefined,
    },
    sortingOrder: {
        type: String as PropType<Sorting['sortingOrder']>,
        default: undefined,
    },
    pagination: {
        type: Object as PropType<Pagination>,
        required: true,
    },
})

const emit = defineEmits<{
    (event: 'edit', tenant: Tenant): void
    (event: 'view', tenant: Tenant): void
    (event: 'delete', tenant: Tenant): void
}>()

const sortByVModel = useVModel(props, 'sortBy', emit)
const sortingOrderVModel = useVModel(props, 'sortingOrder', emit)

const totalPages = computed(() => Math.ceil(props.pagination.total / props.pagination.perPage))
</script>

<template>
    <div>
        <VaDataTable
            v-model:sort-by="sortByVModel"
            v-model:sorting-order="sortingOrderVModel"
            :items="tenants"
            :columns="columns"
            :loading="loading"
        >
            <template #cell(name)="{ rowData }">
                <div class="ellipsis max-w-[230px] lg:max-w-[450px]">
                    {{ rowData.name }}
                </div>
            </template>

            <template #cell(code)="{ rowData: tenant }">
                {{ tenant.code }}
            </template>

            <template #cell(email)="{ rowData: tenant }">
                {{ tenant.email }}
            </template>

            <template #cell(phone)="{ rowData: tenant }">
                {{ tenant.phone }}
            </template>

            <template #cell(address)="{ rowData: tenant }">
                {{ tenant.address }}
            </template>


            <template #cell(isActive)="{ rowData: tenant }">
                <TenantsStatusBadge :status="tenant.isActive"/>
            </template>

            <template #cell(updatedAt)="{ rowData: tenant }">
                {{ formatDateTime(tenant.updatedAt) }}
            </template>

            <template #cell(createdAt)="{ rowData: tenant }">
                {{ formatDateTime(tenant.createdAt) }}
            </template>

            <template #cell(actions)="{ rowData: tenant }">
                <div class="flex gap-2 justify-end">
                    <VaButton
                        preset="primary"
                        size="small"
                        color="info"
                        icon="mso-visibility"
                        aria-label="View tenant"
                        @click="$emit('view', tenant as Tenant)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        color="primary"
                        icon="mso-edit"
                        aria-label="Edit tenant"
                        @click="$emit('edit', tenant as Tenant)"
                    />
                    <VaButton
                        preset="primary"
                        size="small"
                        icon="mso-delete"
                        color="danger"
                        aria-label="Delete tenant"
                        @click="$emit('delete', tenant as Tenant)"
                    />
                </div>
            </template>
        </VaDataTable>
        <div class="flex flex-col-reverse md:flex-row gap-2 justify-between items-center py-2">
            <div>
                <b>{{ $props.pagination.total }} results.</b>
                Results per page
                <VaSelect v-model="$props.pagination.perPage" class="!w-20" :options="[10, 50, 100]"/>
            </div>

            <div v-if="totalPages > 1" class="flex">
                <VaButton
                    preset="secondary"
                    icon="va-arrow-left"
                    aria-label="Previous page"
                    :disabled="$props.pagination.page === 1"
                    @click="$props.pagination.page--"
                />
                <VaButton
                    class="mr-2"
                    preset="secondary"
                    icon="va-arrow-right"
                    aria-label="Next page"
                    :disabled="$props.pagination.page === totalPages"
                    @click="$props.pagination.page++"
                />
                <VaPagination
                    v-model="$props.pagination.page"
                    buttons-preset="secondary"
                    :pages="totalPages"
                    :visible-pages="5"
                    :boundary-links="false"
                    :direction-links="false"
                />
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.va-data-table {
    ::v-deep(tbody .va-data-table__table-tr) {
        border-bottom: 1px solid var(--va-background-border);
    }
}
</style>
