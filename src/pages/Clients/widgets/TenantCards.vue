<script setup lang="ts">
import {type PropType} from 'vue'
import {type Tenant} from '../types'
import {VaButton, VaCard, VaCardContent, VaDivider, VaInnerLoading} from "vuestic-ui";
import TenantsStatusBadge from "../components/TenantsStatusBadge.vue";
import {formatDateTime} from "../../../services/utils";

defineProps({
    tenants: {
        type: Array as PropType<Tenant[]>,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
})

defineEmits<{
    (event: 'edit', tenant: Tenant): void
    (event: 'view', tenant: Tenant): void
    (event: 'delete', tenant: Tenant): void
}>()
</script>

<template>
    <VaInnerLoading
        v-if="tenants.length > 0 || loading"
        :loading="loading"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 min-h-[4rem]"
    >
        <VaCard
            v-for="tenant in tenants"
            :key="tenant.id"
            :style="{ '--va-card-outlined-border': '1px solid var(--va-background-element)' }"
            outlined
        >
            <VaCardContent class="flex flex-col h-full">
                <div class="flex justify-between">
                    <div class="text-[var(--va-secondary)]">Code: {{ tenant.code }}</div>
                    <TenantsStatusBadge :status="tenant.isActive"/>
                </div>
                <div class="flex flex-col items-center gap-4 grow">
                    <h4 class="va-h4 text-center self-stretch overflow-hidden line-clamp-2 text-ellipsis">
                        {{ tenant.name }}
                    </h4>
                    <p> <span class="text-[var(--va-secondary)]">Phone: </span> <span>{{ tenant.phone }}</span> </p>
                    <p> <span class="text-[var(--va-secondary)]">Email: </span> <span>{{ tenant.email }}</span> </p>
                    <p> <span class="text-[var(--va-secondary)]">Address: </span> <span>{{ tenant.address }}</span> </p>
                    <p> <span class="text-[var(--va-secondary)]">Created: </span> <span> {{ formatDateTime(tenant.createdAt) }}</span> </p>
                    <p> <span class="text-[var(--va-secondary)]">Updated: {{ formatDateTime(tenant.updatedAt) }}</span> </p>

                </div>
                <VaDivider class="my-6"/>
                <div class="flex justify-between">
                    <VaButton preset="secondary" icon="mso-visibility" color="info" @click="$emit('view', tenant)"/>
                    <VaButton preset="secondary" icon="mso-edit" color="secondary" @click="$emit('edit', tenant)"/>
                    <VaButton preset="secondary" icon="mso-delete" color="danger"
                              @click="$emit('delete', tenant)"/>
                </div>
            </VaCardContent>
        </VaCard>
    </VaInnerLoading>
    <div v-else class="p-4 flex justify-center items-center text-[var(--va-secondary)]">No tenants</div>
</template>
