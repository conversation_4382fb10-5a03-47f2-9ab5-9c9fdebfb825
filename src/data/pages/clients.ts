import api from '../../services/api'
import {Client} from '../../pages/clients/types'

export type Pagination = {
    page: number
    perPage: number
    total: number
}

export type Sorting = {
    sortBy: 'name' | 'updatedAt' | 'createdAt'
    sortingOrder: 'ASC' | 'DESC' | null
}

export const getClients = async (options: Partial<Sorting> & Pagination) => {
    const response = await fetch(api.allClients(options)).then((r) => r.json())
    console.log("response:...........", response)
    return {
        data: response.data || [],
        pagination: response.meta || {page: 1, perPage: 10, total: 0},
    }
}

export const addClient = async (client: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')

    console.log("client:...........", client)
    const {id, createdAt, updatedAt, ...rawPayload} = client

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.allClients(), {
        method: 'POST',
        body: JSON.stringify(payload),
        headers
    })

    const result = await response.json()
    console.log("response:...........", result)

    if (!response.ok) {
        throw new Error(result.message || 'Failed to create client')
    }

    return result.data
}

export const updateClient = async (client: Client) => {
    const headers = new Headers()
    headers.append('Content-Type', 'application/json')
    console.log("client:...........", client)
    const {id, createdAt, updatedAt, ...rawPayload} = client

    const payload = Object.fromEntries(
        Object.entries(rawPayload).filter(
            ([, value]) => value !== undefined && value !== null && value !== ''
        )
    )
    console.log("payload:...........", payload)

    const response = await fetch(api.client(id), {
        method: 'PATCH',
        body: JSON.stringify(payload),
        headers,
    })

    const result = await response.json()
    console.log("response:...........", result)

    if (!response.ok) {
        throw new Error(result.message || 'Failed to update client')
    }

    return result.data
}


export const removeClient = async (client: Client) => {
    const response = await fetch(api.client(client.id), {method: 'DELETE'})
    return response.ok
}

export const getAvailableTenants = async () => {
    const response = await fetch(api.clientTenants())
    const result = await response.json()

    if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch available tenants')
    }

    return result.data
}

export const getAvailableCompanies = async () => {
    const response = await fetch(api.clientCompanies())
    const result = await response.json()

    if (!response.ok) {
        throw new Error(result.message || 'Failed to fetch available companies')
    }

    return result.data
}
