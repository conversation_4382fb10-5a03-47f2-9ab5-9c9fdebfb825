import {Expose, Type} from "class-transformer";
import {ClientType} from "./create-client.dto";
import {Tenant} from "../../tenant/entities/tenant.entity";
import {Company} from "../../company/entities/company.entity";

export class ClientResponseDto {
    @Expose()
    id: string;

    @Expose()
    clientType: ClientType;

    @Expose()
    companyName: string;

    @Expose()
    firstName: string;

    @Expose()
    middleName: string;

    @Expose()
    lastName: string;

    @Expose()
    email: string;

    @Expose()
    phone: string;

    @Expose()
    altPhone?: string;

    @Expose()
    address: string;

    @Expose()
    city: string;

    @Expose()
    country: string;

    @Expose()
    postalCode: string;

    @Expose()
    nationalId?: string;

    @Expose()
    companyRegistrationNo?: string;

    @Expose()
    taxIdNumber?: string;

    @Expose()
    contactPersonName?: string;

    @Expose()
    contactPersonDesignation?: string;

    @Expose()
    createdAt: Date;

    @Expose()
    updatedAt: Date;

    @Expose()
    deletedAt?: Date;

    @Expose()
    @Type(() => Tenant)
    tenants: Tenant[];

    @Expose()
    @Type(() => Company)
    companies: Company[];
}