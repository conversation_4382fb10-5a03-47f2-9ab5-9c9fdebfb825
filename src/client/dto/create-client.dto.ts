import {ApiProperty, ApiPropertyOptional} from "@nestjs/swagger";
import {IsEmail, IsEnum, IsNotEmpty, IsOptional, IsPhoneNumber, IsString, IsUUID, ValidateIf, IsArray, ArrayMinSize} from "class-validator";

export enum ClientType {
    INDIVIDUAL = 'INDIVIDUAL',
    COMPANY = 'COMPANY',
}

export class CreateClientDto {
    @ApiProperty({enum: ClientType, description: 'Whether the client is an individual or a company'})
    @IsEnum(ClientType)
    @IsNotEmpty()
    clientType: ClientType;

    @ApiProperty({description: 'Company name (required for COMPANY type)', required: false})
    @ValidateIf(o => o.clientType === ClientType.COMPANY)
    @IsNotEmpty({message: 'Company name is required for company clients'})
    @IsString()
    companyName?: string;

    @ApiProperty({description: 'First name (required for INDIVIDUAL type)', required: false})
    @ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
    @IsNotEmpty({message: 'First name is required for individual clients'})
    @IsString()
    firstName?: string;

    @ApiPropertyOptional({description: 'Middle name (optional for INDIVIDUAL type)'})
    @IsOptional()
    @IsString()
    middleName?: string;

    @ApiProperty({description: 'Last name (required for INDIVIDUAL type)', required: false})
    @ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
    @IsNotEmpty({message: 'Last name is required for individual clients'})
    @IsString()
    lastName?: string;


    @ApiProperty({description: 'Client email address'})
    @IsEmail()
    email: string;

    @ApiProperty({description: 'Primary phone number'})
    @IsPhoneNumber()
    phone: string;

    @ApiPropertyOptional({description: 'Alternative phone number'})
    @IsOptional()
    @IsPhoneNumber()
    altPhone?: string;

    @ApiProperty({description: 'Street or physical address'})
    @IsString()
    address: string;

    @ApiProperty({description: 'City'})
    @IsString()
    city: string;

    @ApiProperty({description: 'Country'})
    @IsString()
    country: string;

    @ApiProperty({description: 'Postal or ZIP code'})
    @IsString()
    postalCode: string;

    @ApiProperty({description: 'National ID (required for INDIVIDUAL type)', required: false})
    @ValidateIf(o => o.clientType === ClientType.INDIVIDUAL)
    @IsNotEmpty({message: 'National ID is required for individual clients'})
    @IsString()
    nationalId?: string;

    @ApiPropertyOptional({description: 'Company registration number (for companies)'})
    @IsOptional()
    @IsString()
    companyRegistrationNo?: string;

    @ApiPropertyOptional({description: 'Tax Identification Number (TIN)'})
    @IsOptional()
    @IsString()
    taxIdNumber?: string;

    @ApiPropertyOptional({description: 'Name of contact person (for companies)'})
    @IsOptional()
    @IsString()
    contactPersonName?: string;

    @ApiPropertyOptional({description: 'Designation of the contact person'})
    @IsOptional()
    @IsString()
    contactPersonDesignation?: string;

    @ApiProperty({description: 'Array of tenant IDs this client should be associated with', type: [String]})
    @IsArray()
    @ArrayMinSize(1, {message: 'Client must be associated with at least one tenant'})
    @IsUUID('4', {each: true, message: 'Each tenant ID must be a valid UUID'})
    tenantIds: string[];

    @ApiProperty({description: 'Array of company IDs this client should be associated with', type: [String]})
    @IsArray()
    @ArrayMinSize(1, {message: 'Client must be associated with at least one company'})
    @IsUUID('4', {each: true, message: 'Each company ID must be a valid UUID'})
    companyIds: string[];
}
