import {Body, Controller, Delete, Get, Param, ParseUUIDPipe, Patch, Post, Query, UseFilters, Logger} from '@nestjs/common';
import {ClientService} from './client.service';
import {CreateClientDto} from './dto/create-client.dto';
import {UpdateClientDto} from './dto/update-client.dto';
import {ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags} from "@nestjs/swagger";
import {Client} from "./entities/client.entity";
import { HttpExceptionFilter } from '../common/filters/http-exception.filter';

@ApiTags('Client')
@Controller('client')
@UseFilters(HttpExceptionFilter)
export class ClientController {
    private readonly logger = new Logger(ClientController.name);
    constructor(private readonly clientService: ClientService) {
    }

    @Post()
    @ApiOperation({summary: 'Create a new client'})
    @ApiResponse({status: 201, description: 'The client has been successfully created.', type: Client})
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 409, description: 'Client already exists.'})
    create(@Body() createClientDto: CreateClientDto) {
        return this.clientService.create(createClientDto);
    }

    @Get()
    @ApiOperation({summary: 'Retrieve all vehicle Make'})
    @ApiResponse({
        status: 200,
        description: 'The client retrieved successfully.',
        type: Client,
        isArray: true,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Client with name not found.'})
    @ApiQuery({name: 'page', required: false, type: Number, example: 1})
    @ApiQuery({name: 'perPage', required: false, type: Number, example: 10})
    @ApiQuery({name: 'sort', required: false, type: String, example: 'createdAt'})
    @ApiQuery({
        name: 'order',
        required: false,
        enum: ['ASC', 'DESC'],
        example: 'ASC',
    })
    findAll(
        @Query('page') page = 1,
        @Query('perPage') perPage = 10,
        @Query('sort') sort = 'createdAt',
        @Query('order') order: 'ASC' | 'DESC' = 'ASC',) {
        return this.clientService.findAll(+page, +perPage, sort, order);
    }

    @Get(':identifier')
    @ApiOperation({summary: 'Retrieve client by ID or name'})
    @ApiParam({
        name: 'identifier',
        description: 'Client ID (UUID) or name',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0',
    })
    @ApiResponse({
        status: 200,
        description: 'The client retrieved successfully.',
        type: Client,
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Client with name or ID not found.'})
    async findOne(@Param('identifier') identifier: string) {
        return this.clientService.findOneByIdOrName(identifier);
    }

    @Patch(':identifier')
    @ApiOperation({summary: 'Update client'})
    @ApiParam({
        name: 'identifier',
        description: 'Client ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The client successfully updated.',
        type: Client
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Client with ID or name not found.'})
    @ApiResponse({status: 409, description: 'Client with the provided name already exists.'})
    update(
        @Param('identifier') identifier: string,
        @Body() updateClientDto: UpdateClientDto
    ) {
        return this.clientService.update(identifier, updateClientDto);
    }

    @Delete(':id')
    @ApiOperation({summary: 'Delete client'})
    @ApiParam({
        name: 'id',
        description: 'Client ID (UUID)',
        type: String,
        example: 'e5b6a9c3-4e4a-4e8f-8c34-0fc8f403b4d0'
    })
    @ApiResponse({
        status: 200,
        description: 'The client successfully deleted.'
    })
    @ApiResponse({status: 400, description: 'Bad Request.'})
    @ApiResponse({status: 404, description: 'Client with the provided ID not found.'})
    remove(@Param('id', new ParseUUIDPipe()) id: string) {
        return this.clientService.remove(id);
    }

    @Get('options/tenants')
    @ApiOperation({summary: 'Get available tenants for client association'})
    @ApiResponse({
        status: 200,
        description: 'Available tenants retrieved successfully.',
    })
    getAvailableTenants() {
        return this.clientService.getAvailableTenants();
    }

    @Get('options/companies')
    @ApiOperation({summary: 'Get available companies for client association'})
    @ApiResponse({
        status: 200,
        description: 'Available companies retrieved successfully.',
    })
    getAvailableCompanies() {
        return this.clientService.getAvailableCompanies();
    }

}
